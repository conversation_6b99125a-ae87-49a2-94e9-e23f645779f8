[DEFAULT]
host = *************
username =
password =
p_ip = *************
g_ip = *************
d_domain = htportal.infogo.net.cn
c_id = 557663800535285222

; 请注意installgw.py适用于纯净环境独立安装网关
; 脚本命令"docker stop $(docker ps -aq) && docker rm $(docker ps -aq) && docker image prune --force --all && "可以替换只卸载网关的脚本
; 默认会删除所有容器重新安装,如果有重要数据请先备份
; 使用时请删除所有注释行
; host为你要安装的网关ip，脚本会ssh连接网关
; username和password为网关的ssh登录用户名和密码
; p_ip为平台的连接ip
; g_ip为网关的内网ip
; d_domain为平台的域名
; c_id为客户端ID标识 