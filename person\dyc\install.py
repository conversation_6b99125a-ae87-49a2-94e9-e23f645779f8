import os
import sys
import time
import getpass
import requests
import json
import re
from datetime import datetime
from urllib.parse import quote
from playwright.sync_api import sync_playwright

# 添加上层目录到 Python 路径，以便导入 host 模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from host import hosts, ssh_execute_command, check_port, sendDing

import paramiko
import concurrent.futures

# Constants
UserName = getpass.getuser()
USER_DIR = "C:/Users/" + UserName + "/AppData/Local/Google/Chrome2"
PACKAGE_SEARCH_URL = "http://***********:8080"
CONSOLE_URL = "http://***********:8080/console"
COOKIE_FILE = USER_DIR + "/cookies_asec.json"

def find_and_download_package(name):
    with sync_playwright() as p:
        browser = p.chromium.launch_persistent_context(
            headless=False, user_data_dir=USER_DIR, channel="chrome"
        )
        saved_cookies = load_cookies()
        if saved_cookies:
            browser.add_cookies(saved_cookies)

        page = browser.new_page()

        #-------------------------------------------登录ASec和jenkins开始------------------------------------------

        # 跳jenkins
        page.goto(PACKAGE_SEARCH_URL + "/job/asec-platform/")

        time.sleep(2)
        
        try:
            page.locator('a:text("登录")').wait_for(timeout=3000)
            page.click('a:text("登录")')
            print("成功点击 登录 ")

            time.sleep(2)
            # 输入用户名和密码登录
            page.fill("#j_username", "infogo")
            page.fill("#j_password", "infogo@123")
            page.click('button[type="submit"]')
        except Exception as e:
            print(f"点击登录失败，已登录: {e}")
            
        time.sleep(2)

        #--------------------------------------------登录ASec和jenkins结束-----------------------------------------
        # 判断做包是否成功
        page.goto(PACKAGE_SEARCH_URL + "/job/asec-platform/")
        status_title = ''
        try:
            isFinished = False
            count = 1
            while isFinished == False:
                time.sleep(1)
                nameFristDir = quote(name, safe='')
                element = page.locator(f'tr[id="job_{nameFristDir}"] span.jenkins-visually-hidden').first
                status_title = element.inner_text().strip()
                # 检查status_title等于执行中或 不等于成功或失败时
                if status_title == "执行中" or status_title != "成功" and status_title != "失败":
                    print(f"正在做包中：{status_title}！")
                    time.sleep(10)
                    page.goto(PACKAGE_SEARCH_URL + "/job/asec-platform/?t=" + str(count))
                    count += 1
                else:
                    print(f"做包完成：{status_title}！")
                    isFinished = True
        except Exception as e:
            print(f"做包异常: {e}")
            exit(1)

        if status_title != "成功":
            print(f"最新包未做成功：{status_title}！")
            exit(1)
        time.sleep(2)
        
        # 编码
        nameDir = quote(quote(name, safe=''), safe='')
        url = PACKAGE_SEARCH_URL + "/job/asec-platform/job/"+nameDir+"/lastSuccessfulBuild/artifact/"
        if browser.cookies(url):
            save_cookies(browser.cookies(url))

        lcookies = load_cookies()
        browser.close()
        
        try:
            # 动态生成 cookies 字典
            cookies = {}
            for cookie in lcookies:
                cookies[cookie['name']] = cookie['value']

            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Connection': 'keep-alive',
                'Referer': PACKAGE_SEARCH_URL + f"/job/asec-platform/job/{nameDir}/",
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
                'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                # 'Cookie': 'screenResolution=1730x973; JSESSIONID.5d2f48ab=node0r2jh5g6pux6u7srm3z9dpms36.node0; remember-me=aW5mb2dvOjE3NDYzNjIyMzk2MTc6ZTYzZTQ4ZTExY2FhYjBlYWZjYWJjMjJjOWU1YjkxMWNjZmQ5ZTc5MTFjYzJlZGY4MWUzZjRhMDhhN2IxZDhmNQ; asec_token=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; jenkins-timestamper-offset=-28800000',
            }

            response = requests.get(
                url,
                cookies=cookies,
                headers=headers,
                verify=False,
            )
            namePattern = name.replace("/", "-")
            if response.status_code == 200:
                content = response.text
                pattern = rf'href="([^"]*{namePattern}-build\d+-\d+\.tar\.gz)"'
                match = re.search(pattern, content)

                if match:
                    file_url = url + match.group(1)
                    print("下载文件:" + file_url)
                    file_name = match.group(1).split("/")[-1]
                    response = requests.get(
                        file_url, 
                        cookies=cookies,
                        headers=headers,
                        stream=True, 
                        verify=False)
                    with open(file_name, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    # 正则匹配content内容中的时间，格式：<td class="fileSize">2025年4月21日 上午10:37:45</td>
                    pattern = r'<td class="fileSize">(\d{4}年\d{1,2}月\d{1,2}日 \S+)</td>'
                    match2 = re.search(pattern, content)
                    if match2:
                        file_time = match2.group(1)
                        return file_name, file_time
                else:
                    print("未找到符合条件的文件")
            else:
                print(f"Error: Status code {response.status_code}")
        except Exception as e:
            print(f"Error: {e}")
        return None

def get_url_by_name(name):
    file_name, file_time = find_and_download_package(name)
    if file_name:
        return file_name, name, file_time
    return None, None, None

def upload_and_install(package, host_name):
    file_name = package['file_name']
    name = package['name']
    print("开始上传和安装" + host_name + " : " + file_name)
    hostname = host_name
    username = "root" 
    password = "infogoztp123"
    remote_path = "/opt/"
    
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        # file_name替换掉"Offline_"为空，替换掉".tar.gz"为空
        dir_name = file_name.replace("Offline_", "").replace(".tar.gz", "")
        
        namePattern = name.replace("/", "-")
        commands = [
            f"rm -fr /opt/*{namePattern}*",
        ]
        
        for command in commands:
            print(command)
            stdin, stdout, stderr = ssh.exec_command(command)
            print(stdout.read().decode())
        
        ssh.close()
        print("清理升级包完成")

        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        sftp = ssh.open_sftp()
        sftp.put(file_name, remote_path + file_name)
        sftp.close()
        ssh.close()
        print("上传完成" )

        # 上传完成后删除本地文件，给下一个服务器升级需要使用，所以注释掉
        # os.remove(file_name)

        print("开始安装" )
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        # file_name替换掉"Offline_"为空，替换掉".tar.gz"为空
        dir_name = file_name.replace("Offline_", "").replace(".tar.gz", "")

        # 还原安装
        # 检查是否存在 /opt/restore.sh 并执行
        print("检查是否存在 /opt/restore.sh")
        check_restore_script = "if [ -f /opt/restore.sh ]; then echo '存在'; else echo '不存在'; fi"
        stdin, stdout, stderr = ssh.exec_command(check_restore_script)
        exists = stdout.read().decode().strip()

        if exists == "存在":
            print("正在执行 /opt/restore.sh")
            execute_restore_script = "chmod +x /opt/restore.sh && /opt/restore.sh"
            stdin, stdout, stderr = ssh.exec_command(execute_restore_script)
            
            # 输出执行结果
            result = stdout.read().decode()
            error = stderr.read().decode()
            if result:
                print("restore.sh 输出:\n" + result)
            if error:
                print("restore.sh 错误:\n" + error)
        else:
            print("/opt/restore.sh 不存在，跳过执行")
        
        # 新安装
        command = f"cd /opt/ && tar -zxf {file_name} && cd /opt/{dir_name} && chmod +x platform/install.sh && ./platform/install.sh -c release -t private -o false"
        # 升级
        #command = f"cd /opt/ && tar -zxf {file_name} && cd /opt/{dir_name} && chmod +x platform/upgrade.sh && ./platform/upgrade.sh"
        print(command)
        # stdin, stdout, stderr = ssh.exec_command(command)
        # print(stdout.read().decode())
        # 打开一个交互式 shell
        shell = ssh.invoke_shell()
        shell.send(command + '\n')  # 替换为你要输入的值并回车
        # 接收并打印服务器的输出
        while True:
            if shell.recv_ready():
                result = shell.recv(4096).decode('utf-8')
                print(result, end='')

                # 假设服务器输出中包含特定的提示，根据提示输入对应值
                if "请输入用户标识(仅限大小写英文字符):" in result:  # 这里根据实际提示修改
                    shell.send('infogo\n')  # 替换为你要输入的值并回车
                if "请配置平台地址(1.1.1.1 或 xx.com):" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车
                if "请配置用户访问端口(缺省443):" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车
                if "否使用默认Docker网络配置? (y/n):" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车
                if "2) 本地oss" in result:  # 这里根据实际提示修改
                    shell.send('2\n')  # 替换为你要输入的值并回车
                    time.sleep(2)
                if "请设置本地oss存储大小(G,纯数字):" in result:  # 这里根据实际提示修改
                    shell.send('45\n')  # 替换为你要输入的值并回车
                    time.sleep(2)
                if "请输入要用于spa的网卡名称" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车
                if "按回车确认配置,以完成初始化配置" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车
                if "(most recent call last):" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车
                if "Docker网络配置:" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车
                if "Platform installation completed!" in result:  # 这里根据实际提示修改
                    time.sleep(10)
                    break

            if shell.exit_status_ready():
                break

        time.sleep(5)
        print("安装完成")
        
        # 关闭 shell 和 SSH 连接
        shell.close()
        
        ssh.close()
        print(f"{host_name}安装完成，关闭连接")
    except Exception as e:
        print(f"Error: {e}")

def calculate_elapsed_time(start_time, end_time):
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{int(hours)}时{int(minutes)}分{int(seconds)}秒"

def check_is_today(filename):
    date_pattern = r"(\d{4})(\d{2})(\d{2})"
    match = re.search(date_pattern, filename)
    if match:
        year, month, day = map(int, match.groups())
        current_date = datetime.now()
        return (year, month, day) == (
            current_date.year,
            current_date.month,
            current_date.day,
        )
    return False


def save_cookies(cookies):
    with open(COOKIE_FILE, "w") as file:
        json.dump(cookies, file)


def load_cookies():
    try:
        with open(COOKIE_FILE, "r") as file:
            return json.load(file)
    except FileNotFoundError:
        return None


def upgrade_devices(upgrade_packages):
    start_time = time.time()
    messages = [
        f"设备升级开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}"
    ]
    # sendDing(messages[-1])
    host_names = [
        "*************",
    ]

    for index, package in enumerate(upgrade_packages, start=1):
        message = f"升级: {package['file_name']}, 【升级包发布时间：{package['publish_at']}】"
        messages.append(message)
        print(message)
        # sendDing(message)

        try:
            upgrade_single_package(package, host_names)
        except KeyboardInterrupt:
            print(f"\n用户中断了升级过程，当前包: {package['file_name']}")
            print("正在清理资源...")
            # 尝试清理当前包文件
            try:
                if os.path.exists(package['file_name']):
                    os.remove(package['file_name'])
                    print(f"已清理文件: {package['file_name']}")
            except Exception as e:
                print(f"清理文件失败: {e}")
            raise  # 重新抛出以便上层处理
        except Exception as e:
            print(f"升级包 {package['file_name']} 失败: {e}")
            continue  # 继续处理下一个包

        if index < len(upgrade_packages):
            print(f"等待 100 秒后处理下一个包...")
            try:
                time.sleep(100)
            except KeyboardInterrupt:
                print("\n用户中断了等待过程")
                raise

    messages.append("\n".join(host_names))
    #messages.append(hosts)
    # 上传完成后删除本地文件
    os.remove(package['file_name'])
    end_time = time.time()
    final_message = (
        # f"设备升级结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}\n"
        f"共计耗时: {calculate_elapsed_time(start_time, end_time)}"
    )
    messages.append(final_message)
    print(final_message)
    # sendDing(final_message)
    # 钉钉GCH开发群token
    token = "bf7cf7df5d3036d6bfa5d768353d08f849a50980afab292ea180a440d84d5049"
    sendDing("\n".join(messages), token)
def upgrade_single_package(package, hosts):
    # 使用 ThreadPoolExecutor 并发执行 SSH 操作
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = []
        for host in hosts:
            future = executor.submit(upload_and_install, package, host)
            futures.append(future)

        # 获取每个任务的结果，添加异常处理
        try:
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    print(f"任务完成: {result}")
                except Exception as e:
                    print(f"任务执行失败: {e}")
        except KeyboardInterrupt:
            print("\n检测到用户中断，正在停止所有任务...")
            # 取消所有未完成的任务
            for future in futures:
                future.cancel()
            # 等待已启动的任务完成或超时
            concurrent.futures.wait(futures, timeout=30)
            print("所有任务已停止")
            raise  # 重新抛出 KeyboardInterrupt 以便上层处理

def main():
    upgrade_packages = []
    packages_name = [
        "custom/duanyc/1.0.21",
    ]
    for name in packages_name:
        file_name, package_name, publish_at = get_url_by_name(name)
        if not file_name:
            print(f"检查不到包：{name}")
            break
        if file_name and not check_is_today(file_name):
            print(f"检查到不是今天的包：{file_name}")
           #  if input("是否继续升级 (y/n): ").lower() != "y":
            #     continue
        upgrade_packages.append({"file_name": file_name, "name": package_name, "publish_at": publish_at})

    print(upgrade_packages)

    if upgrade_packages:
        try:
            upgrade_devices(upgrade_packages)
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            print("升级过程已停止")
            return 1  # 返回非零退出码表示异常退出
        except Exception as e:
            print(f"升级过程中发生错误: {e}")
            return 1
    else:
        print("没有有效的升级包，升级取消。")

    return 0  # 正常退出


if __name__ == "__main__":
    try:
        exit_code = main()
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n程序被强制中断")
        exit(130)  # 标准的 KeyboardInterrupt 退出码
    except Exception as e:
        print(f"\n程序发生未处理的错误: {e}")
        exit(1)
