from host import ssh_execute_command, check_port, sendDing  # 引入主机信息
import time
import getpass
import requests
import psutil
from datetime import datetime
import json
import re
import threading
import traceback
from urllib.parse import quote
from playwright.sync_api import sync_playwright


def save_cookies(cookiefile, cookies):
    with open(cookiefile, "w") as file:
        json.dump(cookies, file)


def load_cookies(cookiefile):
    try:
        with open(cookiefile, "r") as file:
            return json.load(file)
    except FileNotFoundError:
        return None


def task():
    messages = []
    try:
        UserName = getpass.getuser()
        USER_DIR = "C:/Users/" + UserName + "/AppData/Local/Google/Chrome/User%20Data"
        COOKIE_FILE = USER_DIR + "/cookies_git.json"
        URL = 'https://git.infogo.tech/explore/repos'
        with sync_playwright() as p:
            browser = p.chromium.launch_persistent_context(
                headless=True, user_data_dir=USER_DIR, channel="chrome"
            )
            # 尝试加载 <PERSON>ie
            saved_cookies = load_cookies(COOKIE_FILE)
            if saved_cookies:
                browser.add_cookies(saved_cookies)

            page = browser.new_page()
            page2 = browser.new_page()
            page.goto(URL)
            time.sleep(5)

            Flag = True
            while Flag:
                page2.bring_to_front()
                cangkus = page.query_selector_all('.flex-item-title')
                for cangku in cangkus:
                    cangku_name = cangku.query_selector_all('a')
                    href = cangku_name[1].get_attribute('href')
                    if href.lower().find('asm-web') > -1 or href.lower().find('asm-client') > -1 or href.lower().find(
                            'asm-server') > -1:
                        print(href)
                        # https://git.infogo.tech/ASM-Server/asm_update/pulls?q=&type=all&sort=&state=open&labels=&milestone=0&project=0&assignee=0&poster=0&archived=false
                        url = 'https://git.infogo.tech' + href + '/pulls?q=&type=all&sort=&state=open&labels=&milestone=0&project=0&assignee=0'
                        page2.goto(url)
                        time.sleep(3)
                        prs_item = page2.query_selector_all(
                            'a[class="active item flex-text-inline"]'
                        )
                        if len(prs_item) < 2:
                            continue
                        prs = prs_item[0].text_content()
                        if prs.find('开启中') > -1 and prs.find('0') == -1:
                            allpr = page2.query_selector_all('span[class="truncated-name"]')
                            if len(allpr) > 0:
                                for pr in allpr:
                                    if pr.text_content() == 'custom-ZTP迭代分支-6039.3746.R004.SP2':
                                        messages.append('https://git.infogo.tech' + href + '/pulls')
                                        break

                                    if prs.find('asm_update') > -1 and pr.text_content().find('master') > -1:
                                        messages.append(
                                            "https://git.infogo.tech" + href + "/pulls"
                                        )
                                        break

                # 下一页
                page.bring_to_front()
                time.sleep(2)
                nextpage = page.query_selector('div[class="ui borderless pagination menu"]').query_selector_all('a')
                for nextpage in nextpage:
                    if nextpage.text_content().find('下一页') > -1:
                        if nextpage.is_enabled() and nextpage.get_attribute('class').find('disabled') == -1:
                            Flag = True
                            nextpage.click()
                            time.sleep(2)
                            break
                        else:
                            Flag = False

            if browser.cookies(URL):
                save_cookies(COOKIE_FILE, browser.cookies(URL))

    except Exception as e:
        print(e)
        traceback.print_exc()
        messages = []
    return messages


if __name__ == "__main__":
    token = "e6aecf25879c7e8f22bb2ff388f35e35dc9ab6669fc462267e2ae0b9d9a7b2e4"
    messages = task()
    print(messages)
    tips = "\n以下仓库存在custom-ZTP迭代分支-6039.3746.R004.SP2代码合并请求，请求及时处理！\n"
    if messages:
        sendDing(tips + "\n".join(messages), token=token)
        #sendDing(
        #    tips + "\n".join(messages),
        #    token="4c54d08bd776050c8fed873f157ca6adccd743b00862c324e5ef360bc58d62a9",
        #)
    else:
        sendDing(
            tips + '没有需要处理的请求'
        )

# 钉钉LTS02开发群token
# token = "926353bac58770f7ba146956e77af951c3e27906e8828b1b6a60ac4ede371f17"
# sendDing("\n".join(messages), token)
# 仓库负责人群
# https://oapi.dingtalk.com/robot/send?access_token=4c54d08bd776050c8fed873f157ca6adccd743b00862c324e5ef360bc58d62a9
