import paramiko
import getpass
import configparser
import os

def run_remote_command(host, username, password, p_ip, g_ip, d_domain, c_id):
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    print(f"Connecting to {host}...")
    try:
        ssh.connect(hostname=host, username=username, password=password)
    except Exception as e:
        print(f"连接失败: {e}")
        return

    command = (
        "docker stop $(docker ps -aq) && docker rm $(docker ps -aq) && docker image prune --force --all && "
        "wget http://***********:8080/job/asec-platform/job/custom%252Finfogo%252F1.0.20/lastSuccessfulBuild/artifact/gateway.tar.gz -O ~/gateway.tar.gz && "
        "cd ~ && tar -zxf gateway.tar.gz && cd gateway && chmod +x env.sh && chmod +x install.sh && "
        "./env.sh -o $(pwd)/asec_env.sh && "
        f"./install.sh -m gateway -s run -p {p_ip} -g {g_ip} -d {d_domain} -c {c_id} -e asec_env.sh"
    )
    print(f"Executing remote command:\n{command}")

    try:
        stdin, stdout, stderr = ssh.exec_command(command)
        for line in stdout:
            print(line, end='')
        err = stderr.read().decode()
        if err:
            print("Error:", err)
    except Exception as e:
        print(f"命令执行异常: {e}")
    finally:
        ssh.close()

def get_config_or_input(config, section, key, prompt, is_password=False):
    if config.has_option(section, key):
        value = config.get(section, key)
        if value.strip():
            return value.strip()
    # 没有配置 或 配置为空，提示输入
    if is_password:
        return getpass.getpass(prompt)
    else:
        return input(prompt).strip()

def main():
    config = configparser.ConfigParser()
    config_file = "gw-conf.ini"
    if os.path.exists(config_file):
        config.read(config_file)
    else:
        config.read_dict({"DEFAULT": {}})

    host = get_config_or_input(config, "DEFAULT", "host", "请输入服务器IP: ")
    username = get_config_or_input(config, "DEFAULT", "username", "请输入SSH用户名: ")
    password = get_config_or_input(config, "DEFAULT", "password", "请输入SSH密码: ", is_password=True)
    p_ip = get_config_or_input(config, "DEFAULT", "p_ip", "请输入 -p 参数IP (例如 *************): ")
    g_ip = get_config_or_input(config, "DEFAULT", "g_ip", "请输入 -g 参数IP (例如 *************): ")
    d_domain = get_config_or_input(config, "DEFAULT", "d_domain", "请输入 -d 参数域名 (例如 asec.com.cn): ")
    c_id = get_config_or_input(config, "DEFAULT", "c_id", "请输入 -c 参数客户端ID (例如 557663800535285222): ")

    run_remote_command(host, username, password, p_ip, g_ip, d_domain, c_id)

if __name__ == "__main__":
    main()
