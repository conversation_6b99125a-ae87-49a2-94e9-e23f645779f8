import os
import sys
import time
import getpass
import requests
import json
import re
import shutil
import uuid
import random
import string
from datetime import datetime
from urllib.parse import quote, unquote
from playwright.sync_api import sync_playwright
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from host import hosts, ssh_execute_command, check_port, sendDing
import paramiko
import concurrent.futures
import subprocess

# Constants
UserName = getpass.getuser()
USER_DIR = "C:/Users/" + UserName + "/AppData/Local/Google/Chrome6"
PACKAGE_SEARCH_URL = "http://***********:8080"
CONSOLE_URL = "http://***********:8080/console"
COOKIE_FILE = USER_DIR + "/cookies_asec.json"

def generate_boundary():
    """生成动态的 multipart/form-data boundary"""
    # 生成类似 WebKit 的 boundary 格式
    random_part = ''.join(random.choices(string.ascii_letters + string.digits, k=16))
    return f"----WebKitFormBoundary{random_part}"

def generate_folder_name(windows_version):
    # 使用正则表达式匹配版本号格式
    # 匹配格式: x.x.xx.x-custom-infogo Buildxx
    pattern = r'(\d+)\.(\d+)\.(\d+)\.(\d+)-custom-infogo Build(\d+)'
    match = re.match(pattern, windows_version)

    if match:
        # 提取版本号的各个部分
        _, _, patch, _, build_num = match.groups()
        # 生成 folder_name: infogo_patch_build_num
        folder_name = f"infogo_{patch}_{build_num}"
        print(f"从 windows_version '{windows_version}' 生成 folder_name: '{folder_name}'")
        return folder_name
    else:
        # 如果不匹配预期格式，返回原始值作为备用
        print(f"警告: windows_version '{windows_version}' 不匹配预期格式，使用原始值作为 folder_name")
        return windows_version

def find_and_download_package(name):
    with sync_playwright() as p:
        browser = p.chromium.launch_persistent_context(
            headless=False, user_data_dir=USER_DIR, channel="chrome"
        )
        saved_cookies = load_cookies()
        if saved_cookies:
            browser.add_cookies(saved_cookies)

        page = browser.new_page()

        #-------------------------------------------登录ASec和jenkins开始------------------------------------------

        # 跳jenkins
        page.goto(PACKAGE_SEARCH_URL + "/job/asec-client/")

        time.sleep(2)
        
        try:
            page.locator('a:text("登录")').wait_for(timeout=3000)
            page.click('a:text("登录")')
            print("成功点击 登录 ")

            time.sleep(2)
            # 输入用户名和密码登录
            page.fill("#j_username", "infogo")
            page.fill("#j_password", "infogo@123")
            page.click('button[type="submit"]')
        except Exception as e:
            print(f"点击登录失败，已登录: {e}")
            
        time.sleep(2)

        #--------------------------------------------登录ASec和jenkins结束-----------------------------------------
        # 判断做包是否成功
        page.goto(PACKAGE_SEARCH_URL + "/job/asec-client/")
        status_title = ''
        try:
            isFinished = False
            count = 1
            while isFinished == False:
                time.sleep(1)
                nameFristDir = quote(name, safe='')
                element = page.locator(f'tr[id="job_{nameFristDir}"] span.jenkins-visually-hidden').first
                status_title = element.inner_text().strip()
                # 检查status_title等于执行中或 不等于成功或失败时
                if status_title == "执行中" or status_title != "成功" and status_title != "失败":
                    print(f"正在做包中：{status_title}！")
                    time.sleep(10)
                    page.goto(PACKAGE_SEARCH_URL + "/job/asec-client/?t=" + str(count))
                    count += 1
                else:
                    print(f"做包完成：{status_title}！")
                    isFinished = True
        except Exception as e:
            print(f"做包异常: {e}")
            exit(1)

        if status_title != "成功":
            print(f"最新包未做成功：{status_title}！")
            exit(1)
        time.sleep(2)
        
        message = f"{name}版本做包{status_title}, " 
        nameDir = quote(quote(name, safe=''), safe='')
        packageUrl = PACKAGE_SEARCH_URL + f"/job/asec-client/job/" + nameDir + "/"
        print(f"{message}请访问：{packageUrl}")
        page.goto(packageUrl)

        time.sleep(5)
        artifact_path = "lastSuccessfulBuild/artifact/ASec_Client_Setup"
        escaped_path = re.escape(artifact_path)
        element = page.locator(f"table.fileList a[href^=\"{escaped_path}\"]").first
        downloadUrl = packageUrl + element.get_attribute("href") 
        
        message = message + f"下载地址：{downloadUrl} "
        print(message)
        browser.close()
        
        try:
            print("下载文件:" + downloadUrl)
            file_name = downloadUrl.split("/")[-1]
            file_name = unquote(unquote(file_name))
            response = requests.get(
                downloadUrl, 
                stream=True, 
                verify=False)
            with open(file_name, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            print("保存文件:" + file_name)
            return file_name
        except Exception as e:
            print(f"Error: {e}")

def get_url_by_name(name):
    return find_and_download_package(name)

def upload_and_install(package, host_name, authorization_token=None, folder_name = ''):
    # 如果没有传入 token，尝试从文件加载
    if not authorization_token:
        authorization_token = load_auth_token(host_name)

    # 如果仍然没有 token，返回错误
    if not authorization_token:
        print(f"错误: 没有可用的 Authorization token 用于主机 {host_name}")
        return None

    print(f"开始上传文件到主机: {host_name}")
    print(f"使用 Authorization token: {authorization_token}...")

    try:
        # 使用传入的 authorization_token
        # 注意：不要手动设置 Content-Type，让 requests 自动处理 multipart/form-data 的 boundary
        headers = {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Authorization': authorization_token,
            'Connection': 'keep-alive',
            # 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryhB7aTALxCmR8UNMy',  # 注释掉，让 requests 自动处理
            'Origin': f"https://{host_name}:4430",
            'Referer': f"https://{host_name}:4430/",
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }

        # 确保文件存在
        pkg_file = f'{folder_name}.pkg'
        if not os.path.exists(pkg_file):
            print(f"错误: 文件 {pkg_file} 不存在")
            return False

        # 使用 with 语句确保文件正确关闭
        with open(pkg_file, 'rb') as file_handle:
            # 正确的 files 格式，让 requests 自动处理文件上传和 boundary
            files = {
                'file_name': (None, f'{folder_name}.pkg'),
                'chunk_file': (pkg_file, file_handle, 'application/octet-stream'),
                'length': (None, '1'),
            }

            print(f"开始上传文件到: https://{host_name}:4430/console/v1/upgrade/segmented_upload")
            response = requests.post(
                f"https://{host_name}:4430/console/v1/upgrade/segmented_upload",
                headers=headers,
                files=files,
                verify=False,
            )

        print(f"上传响应: {response.status_code}")
        print(response.text)

        # 检查响应
        if response.status_code == 200:
            try:
                response_json = response.json()
                if response_json.get('code') == 0:
                    print("✅ 文件上传成功")
                    return True
                else:
                    print(f"❌ 服务器返回错误: {response_json.get('msg', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                print("✅ 上传成功（非JSON响应）")
                return True
        else:
            print(f"❌ HTTP 请求失败，状态码: {response.status_code}")
            return False

    except Exception as e:
        print(f"上传失败: {e}")
        return None


def calculate_elapsed_time(start_time, end_time):
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{int(hours)}时{int(minutes)}分{int(seconds)}秒"

def check_is_today(filename):
    date_pattern = r"(\d{4})(\d{2})(\d{2})"
    match = re.search(date_pattern, filename)
    if match:
        year, month, day = map(int, match.groups())
        current_date = datetime.now()
        return (year, month, day) == (
            current_date.year,
            current_date.month,
            current_date.day,
        )
    return False


def save_cookies(cookies):
    with open(COOKIE_FILE, "w") as file:
        json.dump(cookies, file)


def load_cookies():
    try:
        with open(COOKIE_FILE, "r") as file:
            return json.load(file)
    except FileNotFoundError:
        return None

def save_auth_token(token, host_name):
    """保存 Authorization token 到文件"""
    try:
        with open(USER_DIR + "/auth_token_" + host_name +".txt", "w") as file:
            file.write(token)
        print(f"Authorization token 已保存到文件")
    except Exception as e:
        print(f"保存 Authorization token 失败: {e}")

def load_auth_token(host_name):
    """从文件加载 Authorization token"""
    try:
        with open(USER_DIR + "/auth_token_" + host_name +".txt", "r") as file:
            token = file.read().strip()
            if token:
                # 先去掉 "Bearer " 前缀（如果存在）
                token_content = token
                if token.startswith("Bearer "):
                    token_content = token[7:]  # 去掉 "Bearer " (7个字符)

                # 尝试解析 JSON 格式的 token
                try:
                    token_json = json.loads(token_content)
                    if isinstance(token_json, dict) and 'accessToken' in token_json:
                        access_token = token_json['accessToken']
                        print(f"从文件加载 JSON 格式 Authorization token: {access_token[:30]}...")
                        return f"Bearer {access_token}"
                    else:
                        print("JSON 格式的 token 中未找到 accessToken 字段")
                        return None
                except json.JSONDecodeError:
                    # 如果不是 JSON 格式，按原来的方式处理
                    print(f"从文件加载 Authorization token: {token[:30]}...")
                    return token
    except FileNotFoundError:
        print("未找到保存的 Authorization token 文件")
    except Exception as e:
        print(f"加载 Authorization token 失败: {e}")
    return None

def get_auth_token_from_browser(host_name):
    """从浏览器获取认证 token"""
    authorization_token = load_auth_token(host_name)

    # 如果已经有有效的 token，直接返回
    if authorization_token:
        print(f"使用已保存的 token: {authorization_token[:30]}...")
        return authorization_token

    print("开始从浏览器获取新的 token...")

    with sync_playwright() as p:
        browser = p.chromium.launch_persistent_context(
            headless=False, user_data_dir=USER_DIR, channel="chrome", ignore_https_errors=True
        )

        page = browser.new_page()

        # 监听网络请求以获取 Authorization header
        def handle_request(request):
            nonlocal authorization_token
            headers = request.headers

            # 检查各种可能的认证头
            auth_headers = ['authorization', 'x-auth-token', 'x-access-token', 'bearer']
            for header_name in auth_headers:
                if header_name in headers and headers[header_name].startswith('Bearer'):
                    authorization_token = headers[header_name]
                    save_auth_token(authorization_token, host_name)  # 保存新获取的 token
                    print(f"从请求头 {header_name} 获取到 Token: {authorization_token[:30]}...")
                    break

        page.on('request', handle_request)

        try:
            # 跳ASec后台
            page.goto(f"https://{host_name}:4430/#/login?redirect=%23/layout/dashboard")
            time.sleep(1)

            # 检查是否在登录页面
            try:
                username_input = page.locator('input[placeholder="请输入用户名"]')
                username_input.wait_for(state="visible", timeout=2000)
                login_page_detected = True
            except:
                try:
                    print("检测是否登录过期页面")
                    expire_label = page.locator('span:text("登录过期")')
                    expire_label.wait_for(state="visible", timeout=2000)
                    print("检测到登录过期，点击确定按钮")
                    # 点击登录过期弹框中的确定按钮
                    confirm_button = page.locator('div.el-message-box__btns button.el-button--primary')
                    confirm_button.click()
                    print("登录已过期，需要重新登录")
                    login_page_detected = True
                    time.sleep(2)
                except Exception as e:
                    print(f"未检测到登录过期页面，可能已经登录")
                    login_page_detected = False

            if login_page_detected:
                print("检测到登录页面，开始登录")
                # 输入用户名和密码登录
                page.fill('input[placeholder="请输入用户名"]', "admin")
                page.fill('input[placeholder="请输入密码"]', "asdsec@2024")
                page.click('button[type="button"]')
                print("完成管理后台登录")
                time.sleep(3)
            else:
                print("未检测到登录页面，已经登录")

            # 获取登录的Token，传递给Authorization
            # 尝试从多种方式获取 Authorization token
            if not authorization_token:
                try:
                    # 尝试从 localStorage 获取 token
                    token = page.evaluate("() => localStorage.getItem('token') || localStorage.getItem('authToken') || localStorage.getItem('access_token') || localStorage.getItem('accessToken')")
                    if token:
                        authorization_token = f"Bearer {token}"
                        save_auth_token(authorization_token, host_name)  # 保存获取的 token
                        print(f"从 localStorage 获取到 token: {authorization_token[:30]}...")
                except Exception as e:
                    print(f"从 localStorage 获取 token 失败: {e}")

            if not authorization_token:
                try:
                    # 尝试从 sessionStorage 获取 token
                    token = page.evaluate("() => sessionStorage.getItem('token') || sessionStorage.getItem('authToken') || sessionStorage.getItem('access_token') || sessionStorage.getItem('accessToken')")
                    if token:
                        authorization_token = f"Bearer {token}"
                        save_auth_token(authorization_token, host_name)  # 保存获取的 token
                        print(f"从 sessionStorage 获取到 token: {authorization_token[:30]}...")
                except Exception as e:
                    print(f"从 sessionStorage 获取 token 失败: {e}")

            if not authorization_token:
                try:
                    # 尝试从 cookies 获取认证信息
                    cookies = page.context.cookies()
                    for cookie in cookies:
                        if cookie['name'].lower() in ['auth', 'authorization', 'token', 'access_token', 'jwt', 'authtoken', 'accesstoken']:
                            authorization_token = f"Bearer {cookie['value']}"
                            save_auth_token(authorization_token, host_name)  # 保存获取的 token
                            print(f"从 cookie {cookie['name']} 获取到 token: {authorization_token[:30]}...")
                            break
                except Exception as e:
                    print(f"从 cookies 获取 token 失败: {e}")

            # 如果还是没有获取到 token，尝试触发一个 API 请求来获取
            if not authorization_token:
                try:
                    print("尝试触发 API 请求以获取 token...")
                    # 访问一个需要认证的页面来触发 token 请求
                    page.goto(f"https://{host_name}:4430/#/layout/dashboard")
                    time.sleep(2)

                    # 再次尝试从存储中获取
                    token = page.evaluate("() => localStorage.getItem('token') || localStorage.getItem('authToken') || localStorage.getItem('access_token') || localStorage.getItem('accessToken')")
                    if token:
                        authorization_token = f"Bearer {token}"
                        save_auth_token(authorization_token, host_name)  # 保存获取的 token
                        print(f"触发请求后从 localStorage 获取到 token: {authorization_token[:30]}...")
                except Exception as e:
                    print(f"触发 API 请求获取 token 失败: {e}")

            # 如果仍然没有获取到 token，使用默认的 token（作为备用）
            if not authorization_token:
                print("警告: 未能动态获取 token，使用默认 token")
                authorization_token = 'Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************'

        except Exception as e:
            print(f"获取 token 失败: {e}")
            return None
        finally:
            browser.close()

    return authorization_token

def run_pkg_script(file_name, windows_version, folder_name):
    try:
        # 确保文件存在
        if not os.path.exists(file_name):
            raise FileNotFoundError(f"输入文件不存在: {file_name}")

        # 确保pkg.sh脚本存在
        pkg_script = "./cmd/pkg.sh"
        if not os.path.exists(pkg_script):
            raise FileNotFoundError(f"pkg.sh脚本不存在: {pkg_script}")

        # 根据 windows_version 生成对应的 folder_name
        cmd = f"{pkg_script} --folder_name=\"{folder_name}\" --windows_version=\"{windows_version}\" --windows_file=\"{file_name}\""
        print(f"正在执行 {cmd} 处理文件: {file_name}...")

        # 在Windows系统上尝试多种方式执行bash脚本
        bash_executables = [
            'C:\\Program Files\\Git\\bin\\bash.exe',  # Git Bash默认路径（优先尝试）
            'bash',  # 如果bash在PATH中
            'C:\\Program Files (x86)\\Git\\bin\\bash.exe',  # Git Bash 32位版本
            'wsl',  # Windows Subsystem for Linux
        ]

        success = False
        current_dir = os.getcwd()
        print(f"当前工作目录: {current_dir}")

        for bash_exe in bash_executables:
            try:
                print(f"尝试使用: {bash_exe}")
                if bash_exe == 'wsl':
                    # 对于WSL，需要转换路径格式
                    wsl_cmd = cmd.replace('\\', '/').replace('C:', '/mnt/c')
                    result = subprocess.run([bash_exe, '-c', wsl_cmd],
                                          check=True,
                                          cwd=current_dir,
                                          capture_output=True,
                                          text=True,
                                          encoding='utf-8',
                                          errors='ignore')
                else:
                    # 对于其他bash解释器，直接执行
                    result = subprocess.run([bash_exe, '-c', cmd],
                                          check=True,
                                          cwd=current_dir,
                                          capture_output=True,
                                          text=True,
                                          encoding='utf-8',
                                          errors='ignore')

                # 打印脚本输出
                if result.stdout:
                    print("脚本输出:")
                    print(result.stdout)
                if result.stderr:
                    print("脚本错误输出:")
                    print(result.stderr)

                success = True
                print(f"使用 {bash_exe} 执行成功")
                break
            except (subprocess.CalledProcessError, FileNotFoundError) as e:
                print(f"尝试使用 {bash_exe} 失败: {e}")
                continue

        if not success:
            raise Exception("无法找到可用的bash解释器")

        # 检查生成的文件是否存在
        output_file = f"{windows_version}.pkg"
        if os.path.exists(output_file):
            print(f"pkg.sh 执行完成，生成文件: {output_file}")
            return output_file
        else:
            raise Exception(f"脚本执行完成但未找到输出文件: {output_file}")

    except Exception as e:
        print(f"执行 pkg.sh 失败: {e}")
        return None

def upgrade_devices(upgrade_packages):
    start_time = time.time()
    messages = [
        f"客户端上传到平台开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}"
    ]
    # sendDing(messages[-1])
    host_names = [
        "*************",
    ]

    for index, package in enumerate(upgrade_packages, start=1):
        message = f"升级: {package['file_name']}"
        file_name = package['file_name']
        messages.append(message)
        print(message)
        # 生成pkg包文件
        windows_version = file_name.replace("ASec_Client_Setup_", "").replace(".exe", "").replace("_Build", "-custom-infogo Build")
        # 执行命令
        folder_name = generate_folder_name(windows_version)
        run_pkg_script(file_name, windows_version, folder_name)

        # 生成完成后删除本地文件
        try:
            os.remove(file_name)
            print(f"删除文件: {file_name}")
        except FileNotFoundError:
            print(f"文件不存在，跳过删除: {file_name}")
        except Exception as e:
            print(f"删除文件失败: {file_name}, 错误: {e}")

        # 删除目录
        try:
            if os.path.exists(folder_name):
                shutil.rmtree(folder_name)
                print(f"删除目录: {folder_name}")
        except Exception as e:
            print(f"删除目录失败: {folder_name}, 错误: {e}")

        # 删除tar.gz文件
        try:
            if os.path.exists(f"{folder_name}.tar.gz"):
                os.remove(f"{folder_name}.tar.gz")
                print(f"删除文件: {folder_name}.tar.gz")
        except Exception as e:
            print(f"删除文件失败: {folder_name}.tar.gz, 错误: {e}")

        upgrade_single_package(package, host_names, folder_name)
        
        if index < len(upgrade_packages):
            time.sleep(2)
            
        # 删除pkg文件
        try:
            if os.path.exists(f"{folder_name}.pkg"):
                # 调试时注释
                # os.remove(f"{windows_version}.pkg")
                print(f"删除文件: {folder_name}.pkg")
        except Exception as e:
            print(f"删除文件失败: {folder_name}.pkg, 错误: {e}")

    messages.append("\n".join(host_names))
    #messages.append(hosts)
    end_time = time.time()
    final_message = (
        # f"设备升级结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}\n"
        f"共计耗时: {calculate_elapsed_time(start_time, end_time)}"
    )
    messages.append(final_message)
    print(final_message)
    # sendDing(final_message)
    # 钉钉GCH开发群token
    token = "bf7cf7df5d3036d6bfa5d768353d08f849a50980afab292ea180a440d84d5049"
    # 调试时注释
    sendDing("\n".join(messages), token)
def upgrade_single_package(package, hosts, folder_name):
    # 使用 ThreadPoolExecutor 并发执行 SSH 操作
    with concurrent.futures.ThreadPoolExecutor(max_workers=1)  as executor:
        futures = []
        for host in hosts:
            print(f"开始升级: {host}")
            if os.path.exists(USER_DIR + "/auth_token_" + host +".txt"):
                os.remove(USER_DIR + "/auth_token_" + host +".txt")
            
            print("获取认证 token...")
            auth_token = get_auth_token_from_browser(host)  # 使用第一个主机获取 token

            if not auth_token:
                print("错误: 无法获取认证 token，跳过上传")
            else:
                future = executor.submit(upload_and_install, package, host, auth_token, folder_name)
                futures.append(future)
        # 获取每个任务的结果
        for future in concurrent.futures.as_completed(futures):
            print(future.result())

def main():
    upgrade_packages = []
    packages_name = [
        "custom/duanyc/1.0.21",
    ]
    for name in packages_name:
        file_name = get_url_by_name(name)
        if not file_name:
            print(f"检查不到包：{name}")
            breakpoint
        upgrade_packages.append({"file_name": file_name})

    print(upgrade_packages)

    if upgrade_packages:
        upgrade_devices(upgrade_packages)
    else:
        print("没有有效的升级包，升级取消。")


if __name__ == "__main__":
    main()
