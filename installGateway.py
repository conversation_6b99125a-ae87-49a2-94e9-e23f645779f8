import os
import time
import getpass
import json
import re
import random
from datetime import datetime
from urllib.parse import quote
from playwright.sync_api import sync_playwright
from host import hosts, ssh_execute_command, check_port, sendDing
import paramiko
import concurrent.futures

# Constants
UserName = getpass.getuser()
USER_DIR = "C:/Users/" + UserName + "/AppData/Local/Google/Chrome3"
COOKIE_FILE = USER_DIR + "/cookies_asec_gateway.json"

# return_type 为id表示返回网关id，为cmd表示返回网关安装命令
def get_crate_gateway(host_name, return_type):
    with sync_playwright() as p:
        browser = p.chromium.launch_persistent_context(
            headless=False, user_data_dir=USER_DIR, channel="chrome", ignore_https_errors=True
        )
        #saved_cookies = load_cookies()
        #if saved_cookies:
        #    browser.add_cookies(saved_cookies)

        page = browser.new_page()

        #-------------------------------------------登录ASec后台开始------------------------------------------

        # 跳jenkins
        page.goto(f"https://{host_name}:4430/#/login?redirect=%23/layout/dashboard")

        time.sleep(1)
        
        try:
            # 检查是否在登录页面
            try:
                username_input = page.locator('input[placeholder="请输入用户名"]')
                username_input.wait_for(state="visible", timeout=2000)
                login_page_detected = True
            except:
                try:
                    print("检测是否登录过期页面")
                    expire_label = page.locator('span:text("登录过期")')
                    expire_label.wait_for(state="visible", timeout=2000)
                    print("检测到登录过期，点击确定按钮")
                    # 点击登录过期弹框中的确定按钮
                    confirm_button = page.locator('div.el-message-box__btns button.el-button--primary')
                    confirm_button.click()
                    print("登录已过期，需要重新登录")
                    login_page_detected = True
                    time.sleep(2)
                except Exception as e:
                    print(f"未检测到登录过期页面，可能已经登录")
                    login_page_detected = False

            if login_page_detected:
                print("检测到登录页面，开始登录")
                # 输入用户名和密码登录
                page.fill('input[placeholder="请输入用户名"]', "admin")
                page.fill('input[placeholder="请输入密码"]', "asdsec@2024")
                page.click('button[type="button"]')
                print("完成管理后台登录")
                time.sleep(3)
            else:
                print("未检测到登录页面，已经登录")

            #点击系统配置
            page.click('span.t-menu-item-title:text("系统管理")')
            print("点击菜单系统管理")
            time.sleep(1)
            page.click('span.t-menu__content:text("系统配置")')
            print("点击菜单系统配置")
            time.sleep(1)
            page.click('div.card-label:text("组件管理")')
            print("点击菜单组件管理")
            time.sleep(1)

            #判断本地网关是否存在
            # 检查是否本地网关
            try:
                gateway_input = page.locator('span:text("本地网关")')
                gateway_input.wait_for(state="visible", timeout=3000)
                is_exist_gateway = True
            except:
                is_exist_gateway = False

            print(is_exist_gateway)

            if is_exist_gateway:
                print("本地网关已存在！")
            else:
                print("本地网关不存在！")
                page.click('span.t-button__text:text(" 添加组件 ")')
                print("点击添加组件")

                # 等待添加组件页面加载
                time.sleep(2)

                # 输入组件名称
                name_label = page.locator('label.el-form-item__label:text("名称：")')
                name_input = name_label.locator('..').locator('input.el-input__inner')
                name_input.fill("本地网关")
                print("输入组件名称：本地网关")

                # 选择组件类型为gateway
                time.sleep(1)
                # 点击组件类型下拉框
                page.click('input.el-input__inner[placeholder="组件类型"]')
                page.click('span:text("gateway")')

                page.fill('input[placeholder="地址"]', host_name)
                page.fill('input[placeholder="端口"]', '8443')
                page.fill('input[placeholder="请输入网关本地IP地址"]', host_name)

                page.click('span.t-button__text:text(" 确定 ")')
                time.sleep(3)
            
            # 获取网关ID
            page.click('span.el-link__inner:text("复制安装命令 ")')
            print("点击复制安装命令")

            # 等待复制完成
            time.sleep(1)

            # 获取剪切板内容
            clipboard_content = page.evaluate("() => navigator.clipboard.readText()")
            print(f"获取到的安装命令：{clipboard_content}")

            if return_type == "cmd":
                return clipboard_content
            else:
                # 从安装命令中提取网关ID（-c 参数后面的数字）
                import re
                match = re.search(r'-c\s+(\d+)', clipboard_content)
                if match:
                    gateway_id = match.group(1)
                    print(f"提取到的网关ID：{gateway_id}")
                    return gateway_id
                else:
                    print("未能从安装命令中提取到网关ID")
                    return None
        except Exception as e:
            print(f"执行失败: {e}")
            return None

def save_cookies(cookies):
    with open(COOKIE_FILE, "w") as file:
        json.dump(cookies, file)


def load_cookies():
    try:
        with open(COOKIE_FILE, "r") as file:
            return json.load(file)
    except FileNotFoundError:
        return None

def upload_and_install(host_name):
    print("开始安装" + host_name)
    hostname = host_name
    username = "root" 
    password = "infogoztp123"
    remote_path = "/opt/"
    
    try:

        # 上传完成后删除本地文件，给下一个服务器升级需要使用，所以注释掉
        # os.remove(file_name)

        print("开始安装" )
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)

        # 先上传并执行卸载脚本
        print("上传卸载脚本...")
        sftp = ssh.open_sftp()
        uninstall_script_path = "tool/uninstall.sh"
        remote_uninstall_path = "/opt/uninstall.sh"

        try:
            sftp.put(uninstall_script_path, remote_uninstall_path)
            print("卸载脚本上传完成")
        except Exception as e:
            print(f"上传卸载脚本失败: {e}")
            sftp.close()
            ssh.close()
            return

        sftp.close()

        # 执行卸载脚本
        print("执行卸载脚本...")
        uninstall_command = f"chmod +x {remote_uninstall_path} && printf 'y\\ny\\n' | {remote_uninstall_path}"
        stdin, stdout, stderr = ssh.exec_command(uninstall_command)
        uninstall_output = stdout.read().decode()
        print("卸载脚本执行结果:")
        print(uninstall_output)

        # 删除上传的卸载脚本
        stdin, stdout, stderr = ssh.exec_command(f"rm -f {remote_uninstall_path}")
        print("清理卸载脚本完成")

        # 新安装
        command = get_crate_gateway(host_name, "cmd")
        if command == None or command == "":
            print(f"{hostname}未获取到网关，安装失败" )
            return

        #command = f"cd /opt/ && wget --no-check-certificate \"https://{hostname}:443/console/v1/gateway/gateway.tar.gz\" && tar -zxf gateway.tar.gz && cd gateway && chmod +x env.sh && chmod +x install.sh && ./env.sh -o $(pwd)/asec_env.sh && ./install.sh -m gateway -s run -p {hostname} -g {hostname} -c {gateway_id} -e asec_env.sh"
        #command = get_crate_gateway(host_name, "cmd")

        command = "rm -fr /opt/gateway.tar.gz && cd /opt/ && " + command + " && rm -fr /opt/gateway.tar.gz"
        print(command)

        # stdin, stdout, stderr = ssh.exec_command(command)
        # print(stdout.read().decode())
        # 打开一个交互式 shell
        shell = ssh.invoke_shell()
        shell.send(command + '\n')  # 替换为你要输入的值并回车
        # 接收并打印服务器的输出
        while True:
            if shell.recv_ready():
                result = shell.recv(4096).decode('utf-8')
                print(result, end='')
                if "重启 spa-node-ebpf 服务" in result:  # 这里根据实际提示修改
                    time.sleep(10)
                    break

            if shell.exit_status_ready():
                break

        time.sleep(5)
        print("安装完成")
        
        # 关闭 shell 和 SSH 连接
        shell.close()
        
        ssh.close()
        print(f"{host_name}安装完成，关闭连接")
    except Exception as e:
        print(f"Error: {e}")

def calculate_elapsed_time(start_time, end_time):
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{int(hours)}时{int(minutes)}分{int(seconds)}秒"

def upgrade_devices():
    start_time = time.time()
    messages = [
        f"本地网关安装开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}"
    ]
    # sendDing(messages[-1])
    # 调试时注释
    host_names = [
        "*************",
        "*************",
    ]

    upgrade_single_package(host_names)

    messages.append("\n".join(host_names))
    end_time = time.time()
    final_message = (
        # f"本地网关安装结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}\n"
        f"共计耗时: {calculate_elapsed_time(start_time, end_time)}"
    )
    messages.append(final_message)
    print(final_message)
    # 钉钉GCH开发群token
    token = "7f2829a52f1016a6a5fa01f837ade7d9fa9daf1fee2ccdfec118cf4568c5c211"
    print("\n".join(messages))
    # 调试时注释
    # sendDing("\n".join(messages), token)
def upgrade_single_package(hosts):
    # 使用 ThreadPoolExecutor 并发执行 SSH 操作 
    with concurrent.futures.ThreadPoolExecutor(max_workers=1)  as executor: 
        futures = [] 
        for host in hosts:
            future = executor.submit(upload_and_install,  host) 
            futures.append(future)
        # 获取每个任务的结果 
        for future in concurrent.futures.as_completed(futures):  
            print(future.result())              

def main():
    upgrade_devices()

if __name__ == "__main__":
    main()
