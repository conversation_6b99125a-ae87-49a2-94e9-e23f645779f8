import os
import time
import getpass
import requests
import json
import re
from datetime import datetime
from urllib.parse import quote
from playwright.sync_api import sync_playwright
from host import hosts, ssh_execute_command, check_port, sendDing
import paramiko

def install(host_name):
    hostname = host_name
    username = "root" 
    password = "infogoztp123"
    
    try:
        print(hostname)
        print("开始安装" )
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)

        # 打开一个交互式 shell
        # shell = ssh.invoke_shell()
        # shell.send('cd /opt/ && tar -zxf custom-infogo-1.0.19-build34-20250509.tar.gz && cd /opt/custom-infogo-1.0.19-build34-20250509 && chmod +x platform/install.sh && ./platform/install.sh -c release -t private -o false\n')  # 替换为你要输入的值并回车
        command = f"cd /opt/ && tar -zxf custom-infogo-1.0.19-build34-20250509.tar.gz && cd /opt/custom-infogo-1.0.19-build34-20250509 && chmod +x platform/install.sh && ./platform/install.sh -c release -t private -o false"
        print(command)
        # stdin, stdout, stderr = ssh.exec_command(command)
        # print(stdout.read().decode())
        shell = ssh.invoke_shell()
        shell.send(command + '\n')  # 替换为你要输入的值并回车

        # 接收并打印服务器的输出
        while True:
            if shell.recv_ready():
                result = shell.recv(4096).decode('utf-8')
                print(result, end='')

                # 假设服务器输出中包含特定的提示，根据提示输入对应值
                if "请输入用户标识(仅限大小写英文字符):" in result:  # 这里根据实际提示修改
                    shell.send('infogo\n')  # 替换为你要输入的值并回车
                if "请配置平台地址(1.1.1.1 或 xx.com):" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车
                if "请配置用户访问端口(缺省443):" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车
                if "否使用默认Docker网络配置? (y/n):" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车
                if "2) 本地oss" in result:  # 这里根据实际提示修改
                    shell.send('2\n')  # 替换为你要输入的值并回车
                if "请设置本地oss存储大小(G,纯数字):" in result:  # 这里根据实际提示修改
                    shell.send('45\n')  # 替换为你要输入的值并回车
                if "(most recent call last):" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车
                if "Docker网络配置:" in result:  # 这里根据实际提示修改
                    shell.send('\n')  # 替换为你要输入的值并回车

            if shell.exit_status_ready():
                break
        
        print("命令执行完成")
        time.sleep(10)
        # 关闭 shell 和 SSH 连接
        shell.close()
        ssh.close()
        print("安装完成")
    except Exception as e:
        print(f"Error: {e}")


def main():

    install('172.31.92.232')

if __name__ == "__main__":
    main()
