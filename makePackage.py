import os
import time
import getpass
import requests
import json
import re
from datetime import datetime
from urllib.parse import quote
from playwright.sync_api import sync_playwright
from host import hosts, ssh_execute_command, check_port, sendDing
import paramiko

# Constants
UserName = getpass.getuser()
USER_DIR = "C:/Users/" + UserName + "/AppData/Local/Google/Chrome"
PACKAGE_SEARCH_URL = "http://***********:8080"
COOKIE_FILE = USER_DIR + "/cookies_makeasec.json"

def make_package(name, repo):
    with sync_playwright() as p:
        start_time = time.time()
        messages = [
            f"{repo}做包开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}"
        ]
        try:
            browser = p.chromium.launch_persistent_context(
                headless=False, user_data_dir=USER_DIR, channel="chrome"
            )
            # 后续逻辑保持不变...
        except KeyboardInterrupt:
            print("检测到中断，正在关闭浏览器...")
            if 'browser' in locals() and browser:
                browser.close()
            browser = p.chromium.launch_persistent_context(
                headless=False, user_data_dir=USER_DIR, channel="chrome"
            )
            raise
        saved_cookies = load_cookies()
        if saved_cookies:
            browser.add_cookies(saved_cookies)

        page = browser.new_page()

        #-------------------------------------------登录ASec和jenkins开始------------------------------------------

        # 跳jenkins
        page.goto(PACKAGE_SEARCH_URL + "/job/" + repo + "/")

        time.sleep(1)
        
        try:
            page.click('a:text("登录")')
            print("成功点击 登录 ")
        except Exception as e:
            print(f"点击 登录 失败: {e}")


        time.sleep(1)
        # 输入用户名和密码登录
        page.fill("#j_username", "infogo")
        page.fill("#j_password", "infogo@123")
        page.click('button[type="submit"]')
            
        time.sleep(1)

        #--------------------------------------------登录ASec和jenkins结束-----------------------------------------

        # 新增：点击 做包 元素
        try:
            page.click('a[title="Schedule a 构建 with parameters for '+name+'"]')
            print("成功点击做包")
        except Exception as e:
            print(f"点击做包失败: {e}")
            
        time.sleep(1)

        # 新增：点击 build 元素
        try:
            # 调试时注释
            page.click('button[class="jenkins-button jenkins-button--primary jenkins-!-build-color"]')
            print("成功点击Build")
        except Exception as e:
            print(f"点击Build失败: {e}")

        # 编码
        nameDir = quote(quote(name, safe=''), safe='')
        # time.sleep(2)
        # page.goto(PACKAGE_SEARCH_URL + f"/job/" + repo + "/job/" + nameDir + "/")
            
        time.sleep(10)
        page.goto(PACKAGE_SEARCH_URL + "/job/" + repo + "/")
        status_title = ''
        try:
            isFinished = False
            count = 1
            while isFinished == False:
                time.sleep(1)
                nameFristDir = quote(name, safe='')
                element = page.locator(f'tr[id="job_{nameFristDir}"] span.jenkins-visually-hidden').first
                status_title = element.inner_text().strip()
                # 检查status_title等于执行中或 不等于成功或失败时
                if status_title == "执行中" or status_title != "成功" and status_title != "失败":
                    print(f"正在做包中：{status_title}！")
                    time.sleep(10)
                    page.goto(PACKAGE_SEARCH_URL + "/job/" + repo +"/?t=" + str(count))
                    count += 1
                else:
                    print(f"做包完成：{status_title}！")
                    isFinished = True
        except Exception as e:
            print(f"做包异常: {e}")
            exit(1)

        message = f"{name}版本做包{status_title}, " 
        packageUrl = PACKAGE_SEARCH_URL + f"/job/" + repo +"/job/" + nameDir + "/"
        print(f"{message}请访问：{packageUrl}")
        
        if status_title == "成功": 
            time.sleep(2)
            page.goto(packageUrl)

            time.sleep(5)
            if repo == "asec-client":
                artifact_path = "lastSuccessfulBuild/artifact/ASec_Client_Setup"
            else:
                artifact_path = "lastSuccessfulBuild/artifact/custom-infogo"
            escaped_path = re.escape(artifact_path)
            element = page.locator(f"table.fileList a[href^=\"{escaped_path}\"]").first
            downloadUrl = packageUrl + element.get_attribute("href") 
            if repo == "asec-platform":
                buildNum = re.search(r"build(\d+)", downloadUrl).group(1)
                downloadUrl = downloadUrl.replace("lastSuccessfulBuild", f"{buildNum}")
            
            message = message + f"下载地址：{downloadUrl} "
        else:
            pipelineUrl = PACKAGE_SEARCH_URL + f"/job/" + repo + "/job/" + nameDir + "/"
            message = message + f"失败原因请查看：{pipelineUrl} "

        print(message)

        end_time = time.time()
        final_message = (
            f"{message}"
            # f"做包结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}\n"
            f"共计耗时: {calculate_elapsed_time(start_time, end_time)}"
        )
        messages.append(final_message)
        time.sleep(2)
        browser.close()
        time.sleep(10)
        return "\n".join(messages)


def calculate_elapsed_time(start_time, end_time):
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{int(hours)}时{int(minutes)}分{int(seconds)}秒"
def save_cookies(cookies):
    with open(COOKIE_FILE, "w") as file:
        json.dump(cookies, file)


def load_cookies():
    try:
        with open(COOKIE_FILE, "r") as file:
            return json.load(file)
    except FileNotFoundError:
        return None



def main():
    messages = []
    message = make_package("custom/infogo/1.0.21", "asec-platform")
    messages.append(message)

    message = make_package("custom/infogo/1.0.21", "asec-client")
    messages.append(message)
    
    token = "7f2829a52f1016a6a5fa01f837ade7d9fa9daf1fee2ccdfec118cf4568c5c211"
    print("\n".join(messages))
    
    # 调试时注释
    sendDing("\n".join(messages), token)


if __name__ == "__main__":
    main()
