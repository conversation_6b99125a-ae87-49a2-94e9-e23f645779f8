-- SQLite
delete from hosts;

--INSERT INTO hosts (hostname, port, username, password, needs_update,type, remark)
--                          VALUES ('**************', '22', 'root', '', 1, 'ASM', '');
                          
--INSERT INTO hosts (hostname, port, username, password, needs_update, type, remark)
--                          VALUES ('**************', '22', 'root', '', 1, 'ASM', '');
                          
INSERT INTO hosts (hostname, port, username, password, needs_update, type, remark)
                          VALUES ('**************', '22', 'root', '', 1, 'ASG', '');
                          
INSERT INTO hosts (hostname, port, username, password, needs_update, type, remark)
                          VALUES ('**************', '22', 'root', '', 1, 'ASG', '');
-- duanyc
INSERT INTO hosts (hostname, port, username, password, needs_update,type, remark)
                          VALUES ('*************', '22', 'root', '', 1, 'ASM', '');

INSERT INTO hosts (hostname, port, username, password, needs_update,type, remark)
                          VALUES ('*************', '22', 'root', '', 1, 'ASM', '');

INSERT INTO hosts (hostname, port, username, password, needs_update,type, remark)
                          VALUES ('*************', '22', 'root', '', 1, 'ASM', '');

INSERT INTO hosts (hostname, port, username, password, needs_update,type, remark)
                          VALUES ('*************', '22', 'root', '', 1, 'ASM', '');

INSERT INTO hosts (hostname, port, username, password, needs_update,type, remark)
                          VALUES ('*************', '22', 'root', '', 1, 'ASM', '');

INSERT INTO hosts (hostname, port, username, password, needs_update,type, remark)
                          VALUES ('*************', '22', 'root', '', 1, 'ASec', '');

INSERT INTO hosts (hostname, port, username, password, needs_update,type, remark)
                          VALUES ('*************', '22', 'root', '', 1, 'ASec', '');

INSERT INTO hosts (hostname, port, username, password, needs_update,type, remark)
                          VALUES ('*************', '22', 'root', '', 1, 'ASec', '');


delete from hosts where hostname='*************';

select * from hosts;