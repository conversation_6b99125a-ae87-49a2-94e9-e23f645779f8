import paramiko
import sqlite3
import os 
from sqlite3 import Error
import socket
import json
import re
import requests

# 定义数据库文件路径和表名
# 获取当前文件的目录路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 创建 data 目录路径
data_dir = os.path.join(current_dir, "data")
# 创建数据库文件路径
DB_FILE = os.path.join(data_dir, "hosts.db")
TABLE_NAME = "hosts"

# 创建连接并创建表
def create_table():
    try:
        conn = sqlite3.connect(DB_FILE)
        c = conn.cursor()
        c.execute(
            """CREATE TABLE IF NOT EXISTS {} (
                        hostname TEXT,
                        port INTEGER,
                        username TEXT,
                        password TEXT,
                        needs_update INTEGER DEFAULT 0,
                        type TEXT,
                        remark TEXT
                     )""".format(
                TABLE_NAME
            )
        )
        conn.commit()
        return conn
    except Error as e:
        print(e)

# 插入数据
def insert_data(conn, data):
    try:
        c = conn.cursor()
        for host in data:
            c.execute(
                """INSERT INTO {} (hostname, port, username, password, type, remark)
                          VALUES (?, ?, ?, ?, ?, ?)""".format(
                    TABLE_NAME
                ),
                (
                    host["hostname"],
                    host["port"],
                    host["username"],
                    host["password"],
                    host["type"],
                    host["remark"],
                ),
            )
        conn.commit()
    except Error as e:
        print(e)

def getHosts():
    conn = sqlite3.connect(DB_FILE)
    try:
        c = conn.cursor()
        c.execute(
            """SELECT hostname, port, username, password, type, remark
                     FROM {} WHERE needs_update=1""".format(
                TABLE_NAME
            )
        )
        rows = c.fetchall()
        hosts = []
        for row in rows:
            host = {
                "hostname": row[0],
                "port": row[1],
                "username": row[2],
                "password": row[3],
                "type": row[4],
                "remark": row[5],
            }
            hosts.append(host)
        return hosts
    except Error as e:
        print(e)

def ssh_execute_command(hostname, port, username, password, command):
    try:
        # 创建SSH客户端
        client = paramiko.SSHClient()

        # 设置自动添加主机密钥
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        # 连接SSH服务器
        client.connect(hostname=hostname, port=port, username=username, password=password)

        # 执行命令
        stdin, stdout, stderr = client.exec_command(command)

        # 读取命令输出
        output = stdout.read().decode('utf-8', errors='replace')
        error = stderr.read().decode('utf-8', errors='replace')

        # 关闭SSH连接
        client.close()

        return output, error
    except Exception as e:
        return None, str(e)


def sendDing(
    text, token=""
):
    if token == '':
        return 'token is empty'
    text = "通知：" + text
    webhook = "https://oapi.dingtalk.com/robot/send?access_token=" + token
    data = {"msgtype": "text", "text": {"content": text}}
    data_string = json.dumps(data)
    headers = {"Content-Type": "application/json;charset=utf-8"}
    response = requests.post(webhook, headers=headers, data=data_string, verify=True)
    result = json.loads(response.text)
    if result["errcode"] != 0:
        print(result["errmsg"])
    return response.text


def check_port(hostname, port, log = 0):
    try:
        # 创建一个 TCP 套接字
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        # 设置连接超时时间为 2 秒
        sock.settimeout(2)
        # 连接到主机的指定端口
        result = sock.connect_ex((hostname, port))
        # 关闭套接字连接
        sock.close()
        if result == 0:
            if log > 0:
                print(f"端口 {port} 在主机 {hostname} 上是开放的")
            return True
        else:
            if log > 0:
                print(f"端口 {port} 在主机 {hostname} 上是关闭的")
            return False
    except Exception as e:
        print(f"发生错误：{e}")
    return False

# create_table()
hosts = getHosts()
