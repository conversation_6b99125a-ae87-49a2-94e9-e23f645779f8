## 自动升级工具 V0.1
### 1.工具安装步骤
1. 安装tool文件夹中的python安装包
2. 安装Vs_buildTools,选择C++开发
3. 安装VScode（可选）

### 2.添加环境变量到系统Path
1. C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts
2. C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts

### 3.安装项目依赖库
```
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
playwright install chrome
```

### 4.修改data/hosts.db，添加数据
### 5.修改kuaizhao.py 确定需要还原的快照和还原点
### 6.钉钉群的通知助手，将获取的token，替换makePackage.py和install.py的token
### 7.将cmd下的bat，添加到windows计划程序中，可以定时执行
```
package.bat执行会做包、还原快照、升级到服务器
```
### 注意事项
1.首次使用，需要updatebyweb.py 运行后，打开的152界面，进行钉钉扫码登录
2.如果需要使用vscode修改代码，建议安装以下几个插件：
Markdown Preview Enhanced
Markdown Preview Github Styling
Pylance
Python
Python Debugger
SQLite Viewer
SQLite