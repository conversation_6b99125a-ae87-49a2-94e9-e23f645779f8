import os
import time
import getpass
import requests
import json
import re
from datetime import datetime
from urllib.parse import quote
from playwright.sync_api import sync_playwright
from host import hosts, ssh_execute_command, check_port, sendDing
import paramiko

# Constants
UserName = getpass.getuser()
USER_DIR = "C:/Users/" + UserName + "/AppData/Local/Google/Chrome5"
PACKAGE_SEARCH_URL = "http://***********:8080"
CONSOLE_URL = "http://***********:8080/console"
COOKIE_FILE = USER_DIR + "/cookies_asec.json"

def find_and_download_package(name):
    with sync_playwright() as p:
        browser = p.chromium.launch_persistent_context(
            headless=False, user_data_dir=USER_DIR, channel="chrome"
        )
        saved_cookies = load_cookies()
        if saved_cookies:
            browser.add_cookies(saved_cookies)

        page = browser.new_page()

        #-------------------------------------------登录ASec和jenkins开始------------------------------------------

        # 跳jenkins
        page.goto(PACKAGE_SEARCH_URL + "/job/asec-platform/")

        time.sleep(2)
        
        try:
            page.locator('a:text("登录")').wait_for(timeout=3000)
            page.click('a:text("登录")')
            print("成功点击 登录 ")

            time.sleep(2)
            # 输入用户名和密码登录
            page.fill("#j_username", "infogo")
            page.fill("#j_password", "infogo@123")
            page.click('button[type="submit"]')
        except Exception as e:
            print(f"点击登录失败，已登录: {e}")
            
        time.sleep(2)

        #--------------------------------------------登录ASec和jenkins结束-----------------------------------------
        
        # 编码
        nameDir = quote(quote(name, safe=''), safe='')
        url = PACKAGE_SEARCH_URL + "/job/asec-platform/job/"+nameDir+"/lastSuccessfulBuild/artifact/"
        if browser.cookies(url):
            save_cookies(browser.cookies(url))

        lcookies = load_cookies()
        browser.close()
        
        try:
            # 动态生成 cookies 字典
            cookies = {}
            for cookie in lcookies:
                cookies[cookie['name']] = cookie['value']

            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'max-age=0',
                'Connection': 'keep-alive',
                'Referer': PACKAGE_SEARCH_URL + f"/job/asec-platform/job/{nameDir}/",
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
                'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                # 'Cookie': 'screenResolution=1730x973; JSESSIONID.5d2f48ab=node0r2jh5g6pux6u7srm3z9dpms36.node0; remember-me=aW5mb2dvOjE3NDYzNjIyMzk2MTc6ZTYzZTQ4ZTExY2FhYjBlYWZjYWJjMjJjOWU1YjkxMWNjZmQ5ZTc5MTFjYzJlZGY4MWUzZjRhMDhhN2IxZDhmNQ; asec_token=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; jenkins-timestamper-offset=-28800000',
            }

            response = requests.get(
                url,
                cookies=cookies,
                headers=headers,
                verify=False,
            )
            namePattern = name.replace("/", "-")
            if response.status_code == 200:
                content = response.text
                pattern = rf'href="([^"]*{namePattern}-build\d+-\d+\.tar\.gz)"'
                match = re.search(pattern, content)

                if match:
                    file_url = url + match.group(1)
                    print("下载文件:" + file_url)
                    file_name = match.group(1).split("/")[-1]
                    response = requests.get(
                        file_url, 
                        cookies=cookies,
                        headers=headers,
                        stream=True, 
                        verify=False)
                    with open(file_name, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    # 正则匹配content内容中的时间，格式：<td class="fileSize">2025年4月21日 上午10:37:45</td>
                    pattern = r'<td class="fileSize">(\d{4}年\d{1,2}月\d{1,2}日 \S+)</td>'
                    match2 = re.search(pattern, content)
                    if match2:
                        file_time = match2.group(1)
                        return file_name, file_time
                else:
                    print("未找到符合条件的文件")
            else:
                print(f"Error: Status code {response.status_code}")
        except Exception as e:
            print(f"Error: {e}")
        return None

def get_url_by_name(name):
    file_name, file_time = find_and_download_package(name)
    if file_name:
        return file_name, name, file_time
    return None, None, None

def upload_and_install(package, host_name):
    file_name = package['file_name']
    name = package['name']
    print("开始上传和安装" + host_name + " : " + file_name)
    hostname = host_name
    username = "root" 
    password = "infogoztp123"
    remote_path = "/opt/"
    
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        # file_name替换掉"Offline_"为空，替换掉".tar.gz"为空
        dir_name = file_name.replace("Offline_", "").replace(".tar.gz", "")
        
        namePattern = name.replace("/", "-")
        commands = [
            f"rm -fr /opt/*{namePattern}*",
        ]
        
        for command in commands:
            print(command)
            stdin, stdout, stderr = ssh.exec_command(command)
            print(stdout.read().decode())
        
        ssh.close()
        print("清理升级包完成")

        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        sftp = ssh.open_sftp()
        sftp.put(file_name, remote_path + file_name)
        sftp.close()
        ssh.close()
        print("上传完成" )

        # 上传完成后删除本地文件
        os.remove(file_name)

        print("开始安装" )
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(hostname, username=username, password=password)
        # file_name替换掉"Offline_"为空，替换掉".tar.gz"为空
        dir_name = file_name.replace("Offline_", "").replace(".tar.gz", "")
        
        # 新安装
        #command = f"cd /opt/ && tar -zxf {file_name} && cd /opt/{dir_name} && chmod +x platform/install.sh && ./platform/install.sh -c release -t private -o false"
        # 升级
        command = f"cd /opt/ && tar -zxf {file_name} && cd /opt/{dir_name} && chmod +x platform/upgrade.sh && ./platform/upgrade.sh"
        print(command)
        # stdin, stdout, stderr = ssh.exec_command(command)
        # print(stdout.read().decode())
        shell = ssh.invoke_shell()
        shell.send(command + '\n')  # 替换为你要输入的值并回车

        # 接收并打印服务器的输出
        while True:
            if shell.recv_ready():
                result = shell.recv(4096).decode('utf-8')
                print(result, end='')
                if "Upgrade Complete:" in result:  # 这里根据实际提示修改
                    time.sleep(10)
                    break
                
            if shell.exit_status_ready():
                break
        time.sleep(10)
        # 关闭 shell 和 SSH 连接
        shell.close()
        ssh.close()
        print("安装完成")
    except Exception as e:
        print(f"Error: {e}")

def calculate_elapsed_time(start_time, end_time):
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{int(hours)}时{int(minutes)}分{int(seconds)}秒"

def check_is_today(filename):
    date_pattern = r"(\d{4})(\d{2})(\d{2})"
    match = re.search(date_pattern, filename)
    if match:
        year, month, day = map(int, match.groups())
        current_date = datetime.now()
        return (year, month, day) == (
            current_date.year,
            current_date.month,
            current_date.day,
        )
    return False


def save_cookies(cookies):
    with open(COOKIE_FILE, "w") as file:
        json.dump(cookies, file)


def load_cookies():
    try:
        with open(COOKIE_FILE, "r") as file:
            return json.load(file)
    except FileNotFoundError:
        return None


def upgrade_devices(upgrade_packages):
    start_time = time.time()
    messages = [
        f"设备升级开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}"
    ]
    # sendDing(messages[-1])
    host_names = [
        "*************",
    ]

    for index, package in enumerate(upgrade_packages, start=1):
        message = f"升级: {package['file_name']}, 【升级包发布时间：{package['publish_at']}】"
        messages.append(message)
        print(message)
        # sendDing(message)

        upgrade_single_package(package, host_names)
        if index < len(upgrade_packages):
            time.sleep(100)

    messages.append("\n".join(host_names))
    # 上传完成后删除本地文件
    # os.remove(package['file_name'])
    end_time = time.time()
    final_message = (
        f"设备升级结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}\n"
        f"共计耗时: {calculate_elapsed_time(start_time, end_time)}"
    )
    messages.append(final_message)
    print(final_message)
    # sendDing(final_message)
    # 钉钉GCH开发群token
    token = "7f2829a52f1016a6a5fa01f837ade7d9fa9daf1fee2ccdfec118cf4568c5c211"
    sendDing("\n".join(messages), token)


def upgrade_single_package(package, hosts):
    for host in hosts:
        upload_and_install(package, host)


def main():
    upgrade_packages = []
    packages_name = [
        #"20240927_遗留问题.sp3",
        #"20241022_遗留问题.sp4",
        #"ZTPR02S01迭代需求包",
        #"稳定版本.sp5谢柳君",
        "custom/infogo/1.0.19",
    ]
    for name in packages_name:
        file_name, package_name, publish_at = get_url_by_name(name)
        if not file_name:
            print(f"检查不到包：{name}")
            break
        if file_name and not check_is_today(file_name):
            print(f"检查到不是今天的包：{file_name}")
           #  if input("是否继续升级 (y/n): ").lower() != "y":
            #     continue
        upgrade_packages.append({"file_name": file_name, "name": package_name, "publish_at": publish_at})

    print(upgrade_packages)

    if upgrade_packages:
        upgrade_devices(upgrade_packages)
    else:
        print("没有有效的升级包，升级取消。")


if __name__ == "__main__":
    main()
