import time
import getpass
import requests
import json
import re
from datetime import datetime
from urllib.parse import quote
from playwright.sync_api import sync_playwright
from host import hosts, ssh_execute_command, check_port, sendDing

# Constants
UserName = getpass.getuser()
USER_DIR = "C:/Users/" + UserName + "/AppData/Local/Google/Chrome/User Data"
PACKAGE_SEARCH_URL = "https://infogo.tech/api/so?kw="
COOKIE_FILE = USER_DIR + "/cookies.json"

def get_url_by_name(name):
    url = quote(PACKAGE_SEARCH_URL + name, safe="/:?=&", encoding="utf-8")
    try:
        print(url)
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print(response.text)
            data = json.loads(response.text)
        
            url = data["data"][0]["detail"]
            name = data["data"][0]["patch"]
            publish_at = data["data"][0]["publish_at"]
            return url, name, publish_at
        else:
            print(f"Error: Status code {response.status_code}")
    except Exception as e:
        print(f"Error: {e}")
    return None, None


def calculate_elapsed_time(start_time, end_time):
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{int(hours)}时{int(minutes)}分{int(seconds)}秒"


def check_is_today(filename):
    date_pattern = r"(\d{4})(\d{2})(\d{2})"
    match = re.search(date_pattern, filename)
    if match:
        year, month, day = map(int, match.groups())
        current_date = datetime.now()
        return (year, month, day) == (
            current_date.year,
            current_date.month,
            current_date.day,
        )
    return False


def save_cookies(cookies):
    with open(COOKIE_FILE, "w") as file:
        json.dump(cookies, file)


def load_cookies():
    try:
        with open(COOKIE_FILE, "r") as file:
            return json.load(file)
    except FileNotFoundError:
        return None


def upgrade_devices(upgrade_packages):
    start_time = time.time()
    messages = [
        f"设备升级开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}"
    ]
    sendDing(messages[-1])

    for index, package in enumerate(upgrade_packages, start=1):
        message = f"升级: {package['name']}, 【升级包发布时间：{package['publish_at']}】"
        messages.append(message)
        print(message)
        sendDing(message)

        upgrade_single_package(package['url'], messages)
        if index < len(upgrade_packages):
            time.sleep(100)

    end_time = time.time()
    final_message = (
        f"设备升级结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}\n"
        f"共计耗时: {calculate_elapsed_time(start_time, end_time)}"
    )
    messages.append(final_message)
    print(final_message)
    sendDing(final_message)
    # 钉钉GCH开发群token
    token = "e6aecf25879c7e8f22bb2ff388f35e35dc9ab6669fc462267e2ae0b9d9a7b2e4"
    sendDing("\n".join(messages), token)


def upgrade_single_package(package_url, messages):
    with sync_playwright() as p:
        browser = p.chromium.launch_persistent_context(
            headless=False, user_data_dir=USER_DIR, channel="chrome"
        )
        saved_cookies = load_cookies()
        if saved_cookies:
            browser.add_cookies(saved_cookies)

        pages_info = []
        for host_info in hosts:
            if not check_port(host_info["hostname"], 80):
                message = f"设备: {host_info['hostname']} 的80端口未开启"
                print(message)
                sendDing(message)
                # continue

            page = browser.new_page()
            page.goto(package_url)
            time.sleep(1)
            page.click('button[title="云打包"]')
            page.fill("#swal2-input", host_info["hostname"])
            page.click('button[class="swal2-confirm swal2-styled"]')
            pages_info.append({"page": page, "host_info": host_info})

        while pages_info:
            for item in pages_info[:]:
                page = item["page"]
                host_info = item["host_info"]
                try:
                    h1_element = page.query_selector('h2[id="swal2-title"]')
                    if h1_element and h1_element.text_content() == "报告老大":
                        print(f"Host: {host_info['hostname']}")
                        div_element = page.query_selector(
                            'div[id="swal2-html-container"]'
                        )
                        if div_element.text_content() == "任务执行完毕!":
                            output = page.query_selector(
                                'div[id="output"]'
                            ).text_content()
                            output = output.encode("utf-8", errors="ignore").decode("utf-8")
                            if output.find("AsmUpdateSuccess") > -1:
                                message = f"{host_info['hostname']}【{host_info['type']}】: 升级成功！"
                                page.click('button[class="swal2-confirm swal2-styled"]')
                                time.sleep(1)
                                h1_element = page.query_selector('h2[id="swal2-title"]')
                                if h1_element and h1_element.text_content().find("现在重启") > -1:
                                    page.click(
                                        'button[class="swal2-confirm swal2-styled"]'
                                    )
                            else:
                                message = f"{host_info['hostname']}: 升级有点小问题"
                                print(f"{host_info['hostname']}升级报错：{output}")
                        else:
                            message = (
                                f"{host_info['hostname']}: 升级失败，赶紧去看看啦！"
                            )

                        print(message)
                        sendDing(message)
                        page.close()
                        pages_info.remove(item)
                        messages.append(message)
                except Exception as e:
                    print(f"Error processing {host_info['hostname']}: {e}")

            time.sleep(10)

        if browser.cookies(package_url):
            save_cookies(browser.cookies(package_url))


def main():
    upgrade_packages = []
    packages_name = [
        #"20240927_遗留问题.sp3",
        #"20241022_遗留问题.sp4",
        #"ZTPR02S01迭代需求包",
        #"稳定版本.sp5谢柳君",
        "ZTPR02S02迭代需求包谢柳君",
    ]
    for name in packages_name:
        url, name, publish_at = get_url_by_name(name)
        if not url:
            break
        if name and not check_is_today(name):
            print(f"检查到不是今天的包：{name}")
            #continue
           #  if input("是否继续升级 (y/n): ").lower() != "y":
            #     continue
        upgrade_packages.append({"url": url, "name": name, "publish_at": publish_at})

    if upgrade_packages:
        upgrade_devices(upgrade_packages)
    else:
        print("没有有效的升级包，升级取消。")


if __name__ == "__main__":
    main()
