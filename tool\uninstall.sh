#!/bin/bash

set +e  # 允许命令失败，继续执行后续步骤

# 固定的gateway目录路径
gateway_dir="/opt/gateway"

echo "========================================"
echo "ASEC Gateway 卸载脚本"
echo "========================================"
echo ""
echo "警告：此操作将删除以下内容："
echo "1. asec_gateway 容器"
echo "2. apisix 容器"
echo "3. etcd 容器"
echo "4. apisix-dashboard 容器（如果存在）"
echo "5. 相关的 Docker volumes"
echo "6. 整个 gateway 目录: $gateway_dir"
echo ""
echo "注意：spa-node-ebpf 服务将保持不变"
echo ""
read -p "确认要继续吗？(y/N): " confirm

if [[ $confirm != [yY] ]]; then
    echo "操作已取消"
    exit 0
fi

echo ""
echo "开始卸载过程..."

# 停止和删除容器的函数
function stop_and_remove_containers() {
    local compose_dir=$1
    local description=$2
    
    echo ""
    echo "正在处理 $description..."
    
    if [ -d "$compose_dir" ] && [ -f "$compose_dir/docker-compose.yml" ]; then
        echo "进入目录: $compose_dir"
        cd "$compose_dir"
        
        # 尝试使用 docker compose down
        echo "尝试使用 docker compose down 停止服务..."
        if docker compose down --remove-orphans 2>/dev/null; then
            echo "✓ $description 服务已通过 compose 停止并删除"
        else
            echo "⚠ compose down 失败，尝试手动删除容器..."
            
            # 获取当前目录下 compose 文件中定义的容器
            local containers=$(docker compose ps -a --format "{{.Name}}" 2>/dev/null || echo "")
            
            if [ -n "$containers" ]; then
                for container in $containers; do
                    echo "正在删除容器: $container"
                    docker rm -f "$container" 2>/dev/null && echo "✓ 容器 $container 已删除" || echo "⚠ 删除容器 $container 失败或不存在"
                done
            else
                echo "未找到相关容器"
            fi
        fi
    else
        echo "⚠ 目录 $compose_dir 不存在或没有 docker-compose.yml 文件"
    fi
}

# 手动删除指定容器的函数
function remove_specific_containers() {
    local containers="asec_gateway apisix etcd apisix_dashboard"
    
    echo ""
    echo "检查并删除指定的容器..."
    
    for container in $containers; do
        if docker ps -a --format "{{.Names}}" | grep -q "^${container}$"; then
            echo "正在删除容器: $container"
            docker rm -f "$container" 2>/dev/null && echo "✓ 容器 $container 已删除" || echo "⚠ 删除容器 $container 失败"
        else
            echo "容器 $container 不存在"
        fi
    done
}

# 删除容器
stop_and_remove_containers "$gateway_dir/tun-server" "tun-server"
stop_and_remove_containers "$gateway_dir/apisix" "apisix"

# 额外检查并删除指定容器
remove_specific_containers

# 删除volumes
echo ""
echo "正在删除相关的 Docker volumes..."

# 查找可能的 volume 名称
possible_volumes=(
    "apisix_etcd_data"
    "gateway_etcd_data" 
    "etcd_data"
    "$(basename $gateway_dir)_etcd_data"
)

for volume in "${possible_volumes[@]}"; do
    if docker volume ls --format "{{.Name}}" | grep -q "^${volume}$"; then
        echo "正在删除 volume: $volume"
        docker volume rm "$volume" 2>/dev/null && echo "✓ Volume $volume 已删除" || echo "⚠ 删除 volume $volume 失败"
    fi
done

# 清理未使用的 volumes
echo "清理未使用的 volumes..."
docker volume prune -f 2>/dev/null && echo "✓ 未使用的 volumes 已清理" || echo "⚠ 清理 volumes 失败"



# 删除目录
echo ""
echo "========================================"
echo "准备删除 gateway 目录"
echo "========================================"
echo "目录路径: $gateway_dir"
echo "目录大小: $(du -sh "$gateway_dir" 2>/dev/null | cut -f1 || echo "未知")"
echo ""
echo "建议：如果您需要保留配置文件，请先备份以下目录："
echo "- $gateway_dir/tun-server/runningcfg"
echo "- $gateway_dir/apisix/apisix_conf"
echo ""
read -p "确认删除整个 gateway 目录吗？(y/N): " confirm_dir

if [[ $confirm_dir == [yY] ]]; then
    echo "正在删除 gateway 目录..."
    if [ -d "$gateway_dir" ]; then
        rm -rf "$gateway_dir" && echo "✓ gateway 目录已删除" || echo "⚠ 删除目录失败"
    else
        echo "⚠ gateway 目录不存在: $gateway_dir"
    fi
else
    echo "保留 gateway 目录"
fi

echo ""
echo "========================================"
echo "卸载完成！"
echo "========================================"
echo ""
echo "摘要："
echo "✓ 已尝试停止和删除所有相关容器"
echo "✓ 已清理相关的 Docker volumes"
if [[ $confirm_dir == [yY] ]]; then
    echo "✓ 已删除 gateway 目录"
else
    echo "- 保留了 gateway 目录"
fi
echo "✓ spa-node-ebpf 服务保持不变"
echo ""
echo "如需重新安装，请运行 install.sh 脚本"
